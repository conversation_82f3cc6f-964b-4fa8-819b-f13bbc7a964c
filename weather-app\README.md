# 🌤️ 天气预报应用

一个基于和风天气API的现代化天气预报网站，支持实时天气查询和美观的界面展示。

## ✨ 功能特性

- 🌍 支持全球城市天气查询
- 🌡️ 实时温度、体感温度显示
- 💨 风向风速、湿度、气压等详细信息
- 🎨 现代化响应式设计
- 📱 移动端友好
- 🔄 实时数据更新

## 🚀 快速开始

### 1. 获取和风天气API密钥

1. 访问 [和风天气开发者平台](https://dev.qweather.com/)
2. 注册账号并登录
3. 进入 [控制台](https://console.qweather.com/)
4. 创建新项目：
   - 选择"免费订阅"（每天1000次免费调用）
   - 选择"Web API"类型
   - 填写项目信息
5. 获取API密钥（KEY）

### 2. 获取API Host

⚠️ **重要：** 和风天气现在要求使用个人API Host，不再支持公共地址

1. 登录控制台后，访问 [设置页面](https://console.qweather.com/setting)
2. 在"API Host"部分找到您的个人地址（格式如：abc1234xyz.def.qweatherapi.com）
3. 复制这个地址

### 3. 配置API密钥和API Host

#### React版本 (src/App.jsx)
```javascript
// 第19-20行左右，替换这两行：
const API_KEY = '2c8285b29a534a8984343c4bd5d4cc61' // 替换为您的API密钥
const API_HOST = 'your_personal_api_host.qweatherapi.com' // 替换为您的API Host
```

#### 纯HTML版本 (script.js)
```javascript
// 第175行和第185行左右，替换这两行：
const API_KEY = '2c8285b29a534a8984343c4bd5d4cc61'; // 替换为您的API密钥
const API_HOST = 'your_personal_api_host.qweatherapi.com'; // 替换为您的API Host
```

### 4. 运行应用

#### 使用React版本
```bash
cd weather-app
npm install
npm run dev
```

#### 使用纯HTML版本
```bash
cd weather-app
node server.js
```

然后在浏览器中访问 `http://localhost:3000`

## 📖 API说明

本应用使用和风天气API的以下接口：

### 城市搜索API
- **接口**: `https://geoapi.qweather.com/v2/city/lookup`
- **用途**: 根据城市名称获取LocationID
- **参数**:
  - `location`: 城市名称
  - `key`: API密钥

### 实时天气API
- **接口**: `https://devapi.qweather.com/v7/weather/now`
- **用途**: 获取实时天气数据
- **参数**:
  - `location`: LocationID
  - `key`: API密钥

## 🎯 支持的天气类型

应用支持显示以下天气类型的图标：

- ☀️ 晴天
- ⛅ 多云
- 🌤️ 少云
- ☁️ 阴天
- 🌦️ 阵雨
- 🌧️ 雨天
- ⛈️ 雷雨
- ❄️ 雪天
- 🌨️ 雨夹雪
- 🌫️ 雾天
- 😷 霾天
- 💨 沙尘天气

## 🔧 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **React版本**: React 18, Vite
- **API**: 和风天气API v7
- **样式**: 现代CSS Grid/Flexbox布局
- **图标**: Emoji图标系统

## 📱 响应式设计

应用采用响应式设计，支持：
- 桌面端 (1200px+)
- 平板端 (768px - 1199px)
- 移动端 (< 768px)

## 🛠️ 自定义配置

### 修改默认城市
```javascript
// 在相应文件中修改默认城市
const [city, setCity] = useState('北京') // React版本
// 或
value="北京" // HTML版本
```

### 添加更多城市建议
```javascript
const popularCities = [
    '北京', '上海', '广州', '深圳', '杭州', '南京',
    '武汉', '成都', '重庆', '天津', '西安', '青岛'
    // 添加更多城市...
];
```

## 🚨 注意事项

1. **API Host必需**: 必须配置个人API Host，不能使用公共地址
2. **API限制**: 免费版本每天1000次调用限制
3. **HTTPS要求**: 生产环境需要HTTPS协议
4. **跨域问题**: 开发环境可能需要配置代理
5. **缓存策略**: 建议实现数据缓存减少API调用

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 🔗 相关链接

- [和风天气开发者文档](https://dev.qweather.com/docs/)
- [和风天气图标库](https://icons.qweather.com/)
- [和风天气控制台](https://console.qweather.com/)

---

如果这个项目对您有帮助，请给个⭐️支持一下！
