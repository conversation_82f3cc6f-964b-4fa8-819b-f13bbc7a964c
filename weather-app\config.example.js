// 和风天气API配置文件示例
// 复制此文件为 config.js 并填入您的API密钥

const CONFIG = {
    // 和风天气API密钥
    // 获取方式：
    // 1. 访问 https://dev.qweather.com/
    // 2. 注册账号并登录
    // 3. 进入控制台 https://console.qweather.com/
    // 4. 创建新项目，选择"免费订阅"和"Web API"
    // 5. 获取API密钥
    QWEATHER_API_KEY: 'your_qweather_api_key_here',
    
    // API接口地址
    API_ENDPOINTS: {
        // 城市搜索接口
        GEO_API: 'https://geoapi.qweather.com/v2/city/lookup',
        // 实时天气接口（免费版）
        WEATHER_NOW: 'https://devapi.qweather.com/v7/weather/now',
        // 如果您使用的是付费版本，请使用以下地址：
        // WEATHER_NOW: 'https://api.qweather.com/v7/weather/now',
    },
    
    // 默认设置
    DEFAULT_CITY: '北京',
    
    // 语言设置
    LANGUAGE: 'zh', // zh: 中文, en: 英文
    
    // 单位设置
    UNIT: 'm', // m: 公制单位, i: 英制单位
};

// 导出配置（如果在模块环境中使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

// 全局变量（如果在浏览器环境中使用）
if (typeof window !== 'undefined') {
    window.WEATHER_CONFIG = CONFIG;
}
