<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>和风天气API测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #34495e;
        }

        input[type="text"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: #3498db;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
            margin-right: 1rem;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .result {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .success {
            background: #d5f4e6;
            border: 1px solid #27ae60;
            color: #27ae60;
        }

        .error {
            background: #fadbd8;
            border: 1px solid #e74c3c;
            color: #e74c3c;
        }

        .info {
            background: #d6eaf8;
            border: 1px solid #3498db;
            color: #3498db;
        }

        .loading {
            background: #fef9e7;
            border: 1px solid #f39c12;
            color: #f39c12;
        }

        .api-endpoints {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .endpoint {
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌤️ 和风天气API测试工具</h1>
        
        <div class="form-group">
            <label for="apiKey">API密钥 (Key):</label>
            <input type="text" id="apiKey" placeholder="请输入您的和风天气API密钥" value="2c8285b29a534a8984343c4bd5d4cc61">
        </div>

        <div class="form-group">
            <label for="apiHost">API Host (可选):</label>
            <input type="text" id="apiHost" placeholder="例如: abc1234xyz.def.qweatherapi.com (留空使用默认地址)">
        </div>

        <div class="form-group">
            <label for="cityName">测试城市:</label>
            <input type="text" id="cityName" placeholder="请输入城市名称" value="北京">
        </div>

        <button class="btn" onclick="testAPI()">🧪 测试API连接</button>
        <button class="btn" onclick="clearResult()">🗑️ 清空结果</button>

        <div class="api-endpoints">
            <h3>将要测试的API地址:</h3>
            <div class="endpoint">🔍 城市搜索: /geo/v2/city/lookup</div>
            <div class="endpoint">🌤️ 实时天气: /v7/weather/now</div>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let resultDiv = document.getElementById('result');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            
            if (!resultDiv.textContent) {
                resultDiv.className = `result ${type}`;
                resultDiv.textContent = logMessage;
            } else {
                resultDiv.textContent += logMessage;
            }
            
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function clearResult() {
            resultDiv.textContent = '';
            resultDiv.className = 'result';
        }

        async function testAPI() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const apiHost = document.getElementById('apiHost').value.trim();
            const cityName = document.getElementById('cityName').value.trim();

            if (!apiKey) {
                log('❌ 请输入API密钥', 'error');
                return;
            }

            if (!cityName) {
                log('❌ 请输入城市名称', 'error');
                return;
            }

            clearResult();
            log('🚀 开始测试和风天气API...', 'loading');
            log(`📍 测试城市: ${cityName}`, 'info');
            log(`🔑 API密钥: ${apiKey.substring(0, 8)}...`, 'info');

            // 准备API地址
            const geoEndpoints = [];
            const weatherEndpoints = [];

            if (apiHost) {
                geoEndpoints.push(`https://${apiHost}/geo/v2/city/lookup`);
                weatherEndpoints.push(`https://${apiHost}/v7/weather/now`);
                log(`🏠 使用自定义API Host: ${apiHost}`, 'info');
            }

            geoEndpoints.push(
                'https://devapi.qweather.com/geo/v2/city/lookup',
                'https://geoapi.qweather.com/v2/city/lookup'
            );

            weatherEndpoints.push('https://devapi.qweather.com/v7/weather/now');

            // 测试城市搜索API
            log('\n🔍 测试城市搜索API...', 'info');
            let geoData = null;
            let successfulGeoEndpoint = null;

            for (const endpoint of geoEndpoints) {
                try {
                    const url = `${endpoint}?location=${encodeURIComponent(cityName)}&key=${apiKey}`;
                    log(`📡 尝试: ${endpoint}`, 'info');

                    const response = await fetch(url);
                    log(`📊 响应状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');

                    if (response.ok) {
                        geoData = await response.json();
                        log(`📋 响应数据: ${JSON.stringify(geoData, null, 2)}`, 'success');

                        if (geoData.code === '200' && geoData.location && geoData.location.length > 0) {
                            successfulGeoEndpoint = endpoint;
                            log(`✅ 城市搜索成功! 找到 ${geoData.location.length} 个结果`, 'success');
                            log(`🏙️ 城市信息: ${geoData.location[0].name} (ID: ${geoData.location[0].id})`, 'success');
                            break;
                        } else {
                            log(`❌ API返回错误: ${geoData.code} - ${geoData.message || '未知错误'}`, 'error');
                        }
                    }
                } catch (error) {
                    log(`❌ 请求失败: ${error.message}`, 'error');
                }
            }

            if (!geoData || geoData.code !== '200' || !geoData.location || geoData.location.length === 0) {
                log('\n💥 所有城市搜索API都失败了!', 'error');
                log('🔧 建议检查:', 'error');
                log('1. API密钥是否正确', 'error');
                log('2. 网络连接是否正常', 'error');
                log('3. 是否需要使用个人API Host', 'error');
                return;
            }

            // 测试天气API
            const locationId = geoData.location[0].id;
            log(`\n🌤️ 测试天气API (LocationID: ${locationId})...`, 'info');

            for (const endpoint of weatherEndpoints) {
                try {
                    const url = `${endpoint}?location=${locationId}&key=${apiKey}`;
                    log(`📡 尝试: ${endpoint}`, 'info');

                    const response = await fetch(url);
                    log(`📊 响应状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');

                    if (response.ok) {
                        const weatherData = await response.json();
                        log(`📋 响应数据: ${JSON.stringify(weatherData, null, 2)}`, 'success');

                        if (weatherData.code === '200') {
                            log(`✅ 天气数据获取成功!`, 'success');
                            log(`🌡️ 当前温度: ${weatherData.now.temp}°C`, 'success');
                            log(`☁️ 天气状况: ${weatherData.now.text}`, 'success');
                            log(`💨 风向风速: ${weatherData.now.windDir} ${weatherData.now.windSpeed}km/h`, 'success');
                            
                            log('\n🎉 API测试完全成功!', 'success');
                            log('✅ 您的API密钥可以正常使用', 'success');
                            log(`✅ 推荐使用的API地址:`, 'success');
                            log(`   城市搜索: ${successfulGeoEndpoint}`, 'success');
                            log(`   天气数据: ${endpoint}`, 'success');
                            return;
                        } else {
                            log(`❌ 天气API返回错误: ${weatherData.code} - ${weatherData.message || '未知错误'}`, 'error');
                        }
                    }
                } catch (error) {
                    log(`❌ 天气API请求失败: ${error.message}`, 'error');
                }
            }

            log('\n💥 天气API测试失败!', 'error');
        }
    </script>
</body>
</html>
