import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [weather, setWeather] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [city, setCity] = useState('北京')

  // 获取天气数据的函数
  const fetchWeather = async (cityName) => {
    setLoading(true)
    setError(null)

    try {
      // 使用和风天气API
      const API_KEY = '2c8285b29a534a8984343c4bd5d4cc61' // 替换为您的和风天气API密钥
      const API_HOST = 'devapi.qweather.com' // 替换为您的API Host

      // 尝试多个可能的API地址
      const apiEndpoints = [
        `https://${API_HOST}/geo/v2/city/lookup`,
        'https://devapi.qweather.com/geo/v2/city/lookup',
        'https://geoapi.qweather.com/v2/city/lookup'
      ]

      let geoData = null
      let lastError = null

      // 尝试不同的API地址获取城市信息
      for (const endpoint of apiEndpoints) {
        try {
          const geoUrl = `${endpoint}?location=${encodeURIComponent(cityName)}&key=${API_KEY}`
          console.log('尝试请求GeoAPI:', geoUrl)

          const geoResponse = await fetch(geoUrl)

          if (geoResponse.ok) {
            geoData = await geoResponse.json()
            console.log('GeoAPI响应数据:', geoData)

            if (geoData.code === '200' && geoData.location && geoData.location.length > 0) {
              console.log('成功获取城市数据，使用API地址:', endpoint)
              break
            } else {
              lastError = new Error(`API返回错误: ${geoData.code} - ${geoData.message || '未知错误'}`)
            }
          } else {
            lastError = new Error(`API请求失败: ${geoResponse.status} ${geoResponse.statusText}`)
          }
        } catch (error) {
          console.warn('请求失败:', endpoint, error.message)
          lastError = error
        }
      }

      // 如果所有API地址都失败了
      if (!geoData || geoData.code !== '200' || !geoData.location || geoData.location.length === 0) {
        console.error('所有API地址都失败了，使用模拟数据')
        const mockData = generateMockWeatherData(cityName)
        setWeather(mockData)
        setError(`无法连接到和风天气API，使用模拟数据。错误: ${lastError?.message || '网络连接失败'}`)
        return
      }

      const locationId = geoData.location[0].id

      // 获取实时天气数据
      const weatherEndpoints = [
        `https://${API_HOST}/v7/weather/now`,
        'https://devapi.qweather.com/v7/weather/now'
      ]

      let weatherData = null
      for (const endpoint of weatherEndpoints) {
        try {
          const weatherUrl = `${endpoint}?location=${locationId}&key=${API_KEY}`
          console.log('尝试请求天气API:', weatherUrl)

          const weatherResponse = await fetch(weatherUrl)

          if (weatherResponse.ok) {
            weatherData = await weatherResponse.json()
            console.log('天气API响应数据:', weatherData)

            if (weatherData.code === '200') {
              console.log('成功获取天气数据，使用API地址:', endpoint)
              break
            }
          }
        } catch (error) {
          console.warn('天气API请求失败:', endpoint, error.message)
        }
      }

      if (!weatherData || weatherData.code !== '200') {
        throw new Error('无法获取天气数据')
      }

      // 转换和风天气数据格式为应用所需格式
      const convertedData = convertQWeatherData(weatherData, geoData.location[0])
      setWeather(convertedData)

    } catch (err) {
      console.error('获取天气数据失败:', err)
      // 提供模拟数据作为后备
      const mockWeatherData = generateMockWeatherData(cityName)
      setWeather(mockWeatherData)
      setError(`${err.message} (使用模拟数据)`)
    } finally {
      setLoading(false)
    }
  }

  // 生成模拟天气数据
  const generateMockWeatherData = (cityName) => {
    const weatherTypes = [
      { main: 'Clear', description: '晴天', icon: '100' },
      { main: 'Clouds', description: '多云', icon: '101' },
      { main: 'Rain', description: '小雨', icon: '305' },
      { main: 'Snow', description: '雪', icon: '400' }
    ]

    const randomWeather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)]

    return {
      name: cityName,
      main: {
        temp: Math.floor(Math.random() * 30) + 5,
        feels_like: Math.floor(Math.random() * 30) + 5,
        humidity: Math.floor(Math.random() * 50) + 30,
        pressure: Math.floor(Math.random() * 100) + 1000
      },
      weather: [randomWeather],
      wind: {
        speed: Math.floor(Math.random() * 10) + 1,
        dir: '东南风'
      },
      visibility: Math.floor(Math.random() * 5000) + 5000
    }
  }

  // 转换和风天气数据格式
  const convertQWeatherData = (weatherData, locationData) => {
    const now = weatherData.now

    return {
      name: locationData.name,
      main: {
        temp: parseInt(now.temp),
        feels_like: parseInt(now.feelsLike),
        humidity: parseInt(now.humidity),
        pressure: parseInt(now.pressure)
      },
      weather: [{
        main: getWeatherMainFromIcon(now.icon),
        description: now.text,
        icon: now.icon
      }],
      wind: {
        speed: parseFloat(now.windSpeed),
        dir: now.windDir
      },
      visibility: parseFloat(now.vis) * 1000 // 转换为米
    }
  }

  // 根据和风天气图标代码获取主要天气类型
  const getWeatherMainFromIcon = (iconCode) => {
    const iconMap = {
      '100': 'Clear', // 晴
      '101': 'Clouds', // 多云
      '102': 'Clouds', // 少云
      '103': 'Clouds', // 晴间多云
      '104': 'Clouds', // 阴
      '300': 'Rain', // 阵雨
      '301': 'Rain', // 强阵雨
      '302': 'Thunderstorm', // 雷阵雨
      '303': 'Thunderstorm', // 强雷阵雨
      '304': 'Rain', // 雷阵雨伴有冰雹
      '305': 'Rain', // 小雨
      '306': 'Rain', // 中雨
      '307': 'Rain', // 大雨
      '308': 'Rain', // 极端降雨
      '309': 'Drizzle', // 毛毛雨/细雨
      '310': 'Rain', // 暴雨
      '311': 'Rain', // 大暴雨
      '312': 'Rain', // 特大暴雨
      '313': 'Rain', // 冻雨
      '400': 'Snow', // 小雪
      '401': 'Snow', // 中雪
      '402': 'Snow', // 大雪
      '403': 'Snow', // 暴雪
      '404': 'Snow', // 雨夹雪
      '405': 'Snow', // 雨雪天气
      '406': 'Snow', // 阵雨夹雪
      '407': 'Snow', // 阵雪
      '500': 'Mist', // 薄雾
      '501': 'Fog', // 雾
      '502': 'Haze', // 霾
      '503': 'Dust', // 扬沙
      '504': 'Dust', // 浮尘
      '507': 'Dust', // 沙尘暴
      '508': 'Dust', // 强沙尘暴
      '509': 'Fog', // 浓雾
      '510': 'Fog', // 强浓雾
      '511': 'Haze', // 中度霾
      '512': 'Haze', // 重度霾
      '513': 'Haze' // 严重霾
    }

    return iconMap[iconCode] || 'Clear'
  }

  // 组件加载时获取默认城市天气
  useEffect(() => {
    fetchWeather(city)
  }, [])

  // 处理搜索
  const handleSearch = (e) => {
    e.preventDefault()
    if (city.trim()) {
      fetchWeather(city.trim())
    }
  }

  // 获取天气图标 - 适配和风天气图标代码
  const getWeatherIcon = (iconCode) => {
    const iconMap = {
      // 晴天
      '100': '☀️', // 晴
      // 多云
      '101': '⛅', // 多云
      '102': '🌤️', // 少云
      '103': '⛅', // 晴间多云
      '104': '☁️', // 阴
      // 雨天
      '300': '🌦️', // 阵雨
      '301': '🌧️', // 强阵雨
      '302': '⛈️', // 雷阵雨
      '303': '⛈️', // 强雷阵雨
      '304': '⛈️', // 雷阵雨伴有冰雹
      '305': '🌧️', // 小雨
      '306': '🌧️', // 中雨
      '307': '🌧️', // 大雨
      '308': '�️', // 极端降雨
      '309': '�🌦️', // 毛毛雨/细雨
      '310': '🌧️', // 暴雨
      '311': '🌧️', // 大暴雨
      '312': '🌧️', // 特大暴雨
      '313': '🌧️', // 冻雨
      // 雪天
      '400': '❄️', // 小雪
      '401': '❄️', // 中雪
      '402': '❄️', // 大雪
      '403': '❄️', // 暴雪
      '404': '🌨️', // 雨夹雪
      '405': '🌨️', // 雨雪天气
      '406': '🌨️', // 阵雨夹雪
      '407': '❄️', // 阵雪
      // 雾霾等
      '500': '🌫️', // 薄雾
      '501': '🌫️', // 雾
      '502': '😷', // 霾
      '503': '💨', // 扬沙
      '504': '💨', // 浮尘
      '507': '💨', // 沙尘暴
      '508': '💨', // 强沙尘暴
      '509': '🌫️', // 浓雾
      '510': '🌫️', // 强浓雾
      '511': '😷', // 中度霾
      '512': '😷', // 重度霾
      '513': '😷'  // 严重霾
    }
    return iconMap[iconCode] || '🌤️'
  }

  return (
    <div className="weather-app">
      <header className="header">
        <h1>🌤️ 天气预报</h1>
        <form onSubmit={handleSearch} className="search-form">
          <input
            type="text"
            value={city}
            onChange={(e) => setCity(e.target.value)}
            placeholder="输入城市名称..."
            className="search-input"
          />
          <button type="submit" className="search-button" disabled={loading}>
            {loading ? '搜索中...' : '搜索'}
          </button>
        </form>
      </header>

      <main className="main-content">
        {error && (
          <div className="error-message">
            ⚠️ {error}
          </div>
        )}

        {loading && (
          <div className="loading">
            <div className="loading-spinner"></div>
            <p>正在获取天气数据...</p>
          </div>
        )}

        {weather && !loading && (
          <div className="weather-card">
            <div className="weather-header">
              <h2>{weather.name}</h2>
              <div className="weather-icon">
                {getWeatherIcon(weather.weather[0]?.icon)}
              </div>
            </div>

            <div className="weather-main">
              <div className="temperature">
                <span className="temp-value">{Math.round(weather.main.temp)}</span>
                <span className="temp-unit">°C</span>
              </div>
              <div className="weather-description">
                {weather.weather[0]?.description}
              </div>
            </div>

            <div className="weather-details">
              <div className="detail-item">
                <span className="detail-label">体感温度</span>
                <span className="detail-value">{Math.round(weather.main.feels_like)}°C</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">湿度</span>
                <span className="detail-value">{weather.main.humidity}%</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">气压</span>
                <span className="detail-value">{weather.main.pressure} hPa</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">风速</span>
                <span className="detail-value">{weather.wind.speed} m/s</span>
              </div>
              {weather.visibility && (
                <div className="detail-item">
                  <span className="detail-label">能见度</span>
                  <span className="detail-value">{(weather.visibility / 1000).toFixed(1)} km</span>
                </div>
              )}
            </div>
          </div>
        )}
      </main>

      <footer className="footer">
        <p>天气数据更新时间: {new Date().toLocaleString('zh-CN')}</p>
      </footer>
    </div>
  )
}

export default App
