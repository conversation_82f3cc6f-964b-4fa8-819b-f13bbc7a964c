import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [weather, setWeather] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [city, setCity] = useState('北京')

  // 获取天气数据的函数
  const fetchWeather = async (cityName) => {
    setLoading(true)
    setError(null)

    try {
      // 使用免费的天气API (OpenWeatherMap)
      // 注意：在实际使用中，您需要注册并获取API密钥
      const API_KEY = 'demo' // 这里使用demo，实际需要真实的API密钥
      const response = await fetch(
        `https://api.openweathermap.org/data/2.5/weather?q=${cityName}&appid=${API_KEY}&units=metric&lang=zh_cn`
      )

      if (!response.ok) {
        throw new Error('无法获取天气数据')
      }

      const data = await response.json()
      setWeather(data)
    } catch (err) {
      // 由于API密钥是demo，我们提供模拟数据
      const mockWeatherData = {
        name: cityName,
        main: {
          temp: Math.floor(Math.random() * 30) + 5,
          feels_like: Math.floor(Math.random() * 30) + 5,
          humidity: Math.floor(Math.random() * 50) + 30,
          pressure: Math.floor(Math.random() * 100) + 1000
        },
        weather: [{
          main: 'Clear',
          description: '晴天',
          icon: '01d'
        }],
        wind: {
          speed: Math.floor(Math.random() * 10) + 1
        },
        visibility: Math.floor(Math.random() * 5000) + 5000
      }
      setWeather(mockWeatherData)
      setError('使用模拟数据 (需要真实API密钥获取实时数据)')
    } finally {
      setLoading(false)
    }
  }

  // 组件加载时获取默认城市天气
  useEffect(() => {
    fetchWeather(city)
  }, [])

  // 处理搜索
  const handleSearch = (e) => {
    e.preventDefault()
    if (city.trim()) {
      fetchWeather(city.trim())
    }
  }

  // 获取天气图标
  const getWeatherIcon = (iconCode) => {
    const iconMap = {
      '01d': '☀️', '01n': '🌙',
      '02d': '⛅', '02n': '☁️',
      '03d': '☁️', '03n': '☁️',
      '04d': '☁️', '04n': '☁️',
      '09d': '🌧️', '09n': '🌧️',
      '10d': '🌦️', '10n': '🌧️',
      '11d': '⛈️', '11n': '⛈️',
      '13d': '❄️', '13n': '❄️',
      '50d': '🌫️', '50n': '🌫️'
    }
    return iconMap[iconCode] || '🌤️'
  }

  return (
    <div className="weather-app">
      <header className="header">
        <h1>🌤️ 天气预报</h1>
        <form onSubmit={handleSearch} className="search-form">
          <input
            type="text"
            value={city}
            onChange={(e) => setCity(e.target.value)}
            placeholder="输入城市名称..."
            className="search-input"
          />
          <button type="submit" className="search-button" disabled={loading}>
            {loading ? '搜索中...' : '搜索'}
          </button>
        </form>
      </header>

      <main className="main-content">
        {error && (
          <div className="error-message">
            ⚠️ {error}
          </div>
        )}

        {loading && (
          <div className="loading">
            <div className="loading-spinner"></div>
            <p>正在获取天气数据...</p>
          </div>
        )}

        {weather && !loading && (
          <div className="weather-card">
            <div className="weather-header">
              <h2>{weather.name}</h2>
              <div className="weather-icon">
                {getWeatherIcon(weather.weather[0]?.icon)}
              </div>
            </div>

            <div className="weather-main">
              <div className="temperature">
                <span className="temp-value">{Math.round(weather.main.temp)}</span>
                <span className="temp-unit">°C</span>
              </div>
              <div className="weather-description">
                {weather.weather[0]?.description}
              </div>
            </div>

            <div className="weather-details">
              <div className="detail-item">
                <span className="detail-label">体感温度</span>
                <span className="detail-value">{Math.round(weather.main.feels_like)}°C</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">湿度</span>
                <span className="detail-value">{weather.main.humidity}%</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">气压</span>
                <span className="detail-value">{weather.main.pressure} hPa</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">风速</span>
                <span className="detail-value">{weather.wind.speed} m/s</span>
              </div>
              {weather.visibility && (
                <div className="detail-item">
                  <span className="detail-label">能见度</span>
                  <span className="detail-value">{(weather.visibility / 1000).toFixed(1)} km</span>
                </div>
              )}
            </div>
          </div>
        )}
      </main>

      <footer className="footer">
        <p>天气数据更新时间: {new Date().toLocaleString('zh-CN')}</p>
      </footer>
    </div>
  )
}

export default App
