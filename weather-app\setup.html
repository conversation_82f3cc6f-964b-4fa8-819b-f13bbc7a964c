<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气应用配置指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }

        h2 {
            color: #34495e;
            margin: 2rem 0 1rem 0;
            font-size: 1.5rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }

        h3 {
            color: #2980b9;
            margin: 1.5rem 0 0.5rem 0;
        }

        .step {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }

        .step-number {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }

        .highlight {
            background: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .warning {
            background: #e74c3c;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .success {
            background: #27ae60;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .btn {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 0.8rem 1.5rem;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        ul {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        li {
            margin: 0.5rem 0;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .feature-item {
            background: #ecf0f1;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .emoji {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌤️ 天气应用配置指南</h1>

        <div class="success">
            <strong>恭喜！</strong> 您已成功创建了一个现代化的天气预报应用。现在只需要几个简单步骤就可以获取实时天气数据了！
        </div>

        <h2>✨ 应用特性</h2>
        <div class="feature-list">
            <div class="feature-item">
                <div class="emoji">🌍</div>
                <strong>全球城市查询</strong>
            </div>
            <div class="feature-item">
                <div class="emoji">🌡️</div>
                <strong>实时温度显示</strong>
            </div>
            <div class="feature-item">
                <div class="emoji">💨</div>
                <strong>详细天气信息</strong>
            </div>
            <div class="feature-item">
                <div class="emoji">📱</div>
                <strong>响应式设计</strong>
            </div>
        </div>

        <h2>🚀 快速配置</h2>

        <div class="step">
            <span class="step-number">1</span>
            <strong>注册和风天气账号</strong>
            <p>访问 <a href="https://dev.qweather.com/" target="_blank">和风天气开发者平台</a> 注册免费账号</p>
        </div>

        <div class="step">
            <span class="step-number">2</span>
            <strong>创建项目</strong>
            <p>登录后进入 <a href="https://console.qweather.com/" target="_blank">控制台</a>，创建新项目：</p>
            <ul>
                <li>选择 <span class="highlight">免费订阅</span>（每天1000次免费调用）</li>
                <li>选择 <span class="highlight">Web API</span> 类型</li>
                <li>填写项目基本信息</li>
            </ul>
        </div>

        <div class="step">
            <span class="step-number">3</span>
            <strong>获取API密钥</strong>
            <p>项目创建成功后，复制您的API密钥（KEY）</p>
        </div>

        <div class="step">
            <span class="step-number">4</span>
            <strong>配置应用</strong>
            <p>根据您使用的版本，在相应文件中替换API密钥：</p>
            
            <h3>React版本 (src/App.jsx)</h3>
            <div class="code-block">
// 第18行左右，找到这行代码：
const API_KEY = 'your_qweather_api_key'

// 替换为您的真实API密钥：
const API_KEY = '您的API密钥'
            </div>

            <h3>纯HTML版本 (script.js)</h3>
            <div class="code-block">
// 第139行左右，找到这行代码：
const API_KEY = 'your_qweather_api_key';

// 替换为您的真实API密钥：
const API_KEY = '您的API密钥';
            </div>
        </div>

        <div class="step">
            <span class="step-number">5</span>
            <strong>启动应用</strong>
            <p>保存文件后，刷新浏览器页面即可看到实时天气数据！</p>
        </div>

        <div class="warning">
            <strong>⚠️ 重要提示：</strong>
            <ul>
                <li>请妥善保管您的API密钥，不要在公开场合泄露</li>
                <li>免费版本每天有1000次调用限制</li>
                <li>如果超出限制，可以考虑升级到付费版本</li>
            </ul>
        </div>

        <h2>🎯 支持的天气类型</h2>
        <p>应用支持显示多种天气状况：</p>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 0.5rem; margin: 1rem 0;">
            <span>☀️ 晴天</span>
            <span>⛅ 多云</span>
            <span>🌤️ 少云</span>
            <span>☁️ 阴天</span>
            <span>🌦️ 阵雨</span>
            <span>🌧️ 雨天</span>
            <span>⛈️ 雷雨</span>
            <span>❄️ 雪天</span>
            <span>🌨️ 雨夹雪</span>
            <span>🌫️ 雾天</span>
            <span>😷 霾天</span>
            <span>💨 沙尘</span>
        </div>

        <h2>🔗 相关链接</h2>
        <div style="text-align: center; margin: 2rem 0;">
            <a href="https://dev.qweather.com/docs/" target="_blank" class="btn">📖 API文档</a>
            <a href="https://console.qweather.com/" target="_blank" class="btn">🎛️ 控制台</a>
            <a href="https://icons.qweather.com/" target="_blank" class="btn">🎨 图标库</a>
            <a href="index.html" class="btn btn-success">🌤️ 返回应用</a>
        </div>

        <div style="text-align: center; margin-top: 2rem; color: #7f8c8d;">
            <p>如果您在配置过程中遇到问题，请参考 <a href="https://dev.qweather.com/docs/" target="_blank">官方文档</a> 或联系技术支持。</p>
        </div>
    </div>
</body>
</html>
