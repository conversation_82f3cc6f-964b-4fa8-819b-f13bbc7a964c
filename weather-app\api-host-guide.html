<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Host 配置指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #e74c3c;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }

        .alert {
            background: #fff5f5;
            border: 2px solid #e74c3c;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            color: #c0392b;
        }

        .alert h2 {
            margin-bottom: 1rem;
            color: #e74c3c;
        }

        .step {
            background: #f8f9fa;
            border-left: 4px solid #e74c3c;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }

        .step-number {
            background: #e74c3c;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }

        .highlight {
            background: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .btn {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 0.8rem 1.5rem;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .screenshot {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 1rem 0;
            max-width: 100%;
        }

        ul {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        li {
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 API Host 配置指南</h1>

        <div class="alert">
            <h2>⚠️ 重要变更通知</h2>
            <p><strong>和风天气已停止公共API地址服务</strong>，现在要求所有开发者使用个人的API Host。这是为了提供更高的安全性和保护开发者隐私。</p>
            <p>如果您看到403或404错误，很可能是因为没有配置正确的API Host。</p>
        </div>

        <h2>🤔 什么是API Host？</h2>
        <p>API Host是您专属的API请求地址，格式类似：<span class="highlight">abc1234xyz.def.qweatherapi.com</span></p>
        <p>每个开发者账号都有唯一的API Host，这提供了更高的安全性。</p>

        <h2>📋 如何获取API Host</h2>

        <div class="step">
            <span class="step-number">1</span>
            <strong>登录和风天气控制台</strong>
            <p>访问 <a href="https://console.qweather.com" target="_blank">https://console.qweather.com</a> 并登录您的账号</p>
        </div>

        <div class="step">
            <span class="step-number">2</span>
            <strong>进入设置页面</strong>
            <p>点击左侧菜单的"设置"或直接访问 <a href="https://console.qweather.com/setting" target="_blank">https://console.qweather.com/setting</a></p>
        </div>

        <div class="step">
            <span class="step-number">3</span>
            <strong>查找API Host</strong>
            <p>在设置页面中找到"API Host"部分，您会看到类似这样的地址：</p>
            <div class="code-block">abc1234xyz.def.qweatherapi.com</div>
            <p>复制这个完整的地址</p>
        </div>

        <div class="step">
            <span class="step-number">4</span>
            <strong>配置到应用中</strong>
            <p>将API Host配置到您的天气应用中：</p>
            
            <h3>React版本 (src/App.jsx)</h3>
            <div class="code-block">
// 找到这行代码：
const API_HOST = 'your_personal_api_host.qweatherapi.com'

// 替换为您的API Host：
const API_HOST = 'abc1234xyz.def.qweatherapi.com'
            </div>

            <h3>HTML版本 (script.js)</h3>
            <div class="code-block">
// 找到这行代码：
const API_HOST = 'your_personal_api_host.qweatherapi.com';

// 替换为您的API Host：
const API_HOST = 'abc1234xyz.def.qweatherapi.com';
            </div>
        </div>

        <h2>🧪 验证配置</h2>
        <p>配置完成后，您可以：</p>
        <ul>
            <li>刷新天气应用页面，查看是否能正常获取天气数据</li>
            <li>使用 <a href="test-api.html">API测试工具</a> 验证配置是否正确</li>
            <li>查看浏览器控制台，确认没有403或404错误</li>
        </ul>

        <h2>❓ 常见问题</h2>
        
        <h3>Q: 为什么要使用API Host？</h3>
        <p>A: 这是和风天气的安全升级，提供更好的隐私保护和安全性。即使API密钥泄露，没有API Host也无法访问数据。</p>

        <h3>Q: 我找不到API Host怎么办？</h3>
        <p>A: 请确保您已经创建了项目并获取了API密钥。API Host会在项目创建后自动生成。</p>

        <h3>Q: 可以使用旧的公共地址吗？</h3>
        <p>A: 不可以。和风天气已经停止了公共API地址的服务，必须使用个人API Host。</p>

        <div style="text-align: center; margin: 2rem 0;">
            <a href="https://console.qweather.com/setting" target="_blank" class="btn">🔧 获取API Host</a>
            <a href="test-api.html" class="btn btn-success">🧪 测试API</a>
            <a href="index.html" class="btn">🌤️ 返回应用</a>
        </div>

        <div style="text-align: center; margin-top: 2rem; color: #7f8c8d;">
            <p>如果您在配置过程中遇到问题，请参考 <a href="https://dev.qweather.com/docs/" target="_blank">官方文档</a> 或联系技术支持。</p>
        </div>
    </div>
</body>
</html>
