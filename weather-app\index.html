<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌤️ 天气预报</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            color: #333;
        }

        .weather-app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            padding: 2rem;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            margin: 0 0 1.5rem 0;
            font-size: 2.5rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .search-form {
            display: flex;
            justify-content: center;
            gap: 1rem;
            max-width: 400px;
            margin: 0 auto;
        }

        .search-input {
            flex: 1;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            background: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            transform: translateY(-2px);
        }

        .search-button {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 25px;
            background: #00b894;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 184, 148, 0.3);
        }

        .search-button:hover:not(:disabled) {
            background: #00a085;
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 184, 148, 0.4);
        }

        .search-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .main-content {
            padding: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
        }

        .error-message {
            background: rgba(255, 107, 107, 0.9);
            color: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .loading {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .weather-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            max-width: 400px;
            width: 100%;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .weather-header {
            text-align: center;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .weather-header h2 {
            margin: 0;
            font-size: 1.8rem;
            color: #2d3436;
        }

        .weather-icon {
            font-size: 3rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .weather-main {
            text-align: center;
            margin-bottom: 2rem;
        }

        .temperature {
            display: flex;
            align-items: baseline;
            justify-content: center;
            margin-bottom: 0.5rem;
        }

        .temp-value {
            font-size: 4rem;
            font-weight: 300;
            color: #2d3436;
        }

        .temp-unit {
            font-size: 2rem;
            color: #636e72;
            margin-left: 0.5rem;
        }

        .weather-description {
            font-size: 1.2rem;
            color: #636e72;
            text-transform: capitalize;
        }

        .weather-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem;
            background: rgba(116, 185, 255, 0.1);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .detail-item:hover {
            background: rgba(116, 185, 255, 0.2);
            transform: translateY(-2px);
        }

        .detail-label {
            font-size: 0.9rem;
            color: #636e72;
            margin-bottom: 0.5rem;
        }

        .detail-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3436;
        }

        .footer {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .hidden {
            display: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .search-form {
                flex-direction: column;
                align-items: center;
            }

            .search-input {
                width: 100%;
                max-width: 300px;
            }

            .weather-card {
                margin: 0 1rem;
                padding: 1.5rem;
            }

            .temp-value {
                font-size: 3rem;
            }

            .weather-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="weather-app">
        <header class="header">
            <h1>🌤️ 天气预报</h1>
            <form class="search-form" id="searchForm">
                <input
                    type="text"
                    id="cityInput"
                    placeholder="输入城市名称..."
                    class="search-input"
                    value="北京"
                />
                <button type="submit" class="search-button" id="searchButton">
                    搜索
                </button>
            </form>
        </header>

        <main class="main-content">
            <div id="errorMessage" class="error-message hidden"></div>

            <div id="loading" class="loading hidden">
                <div class="loading-spinner"></div>
                <p>正在获取天气数据...</p>
            </div>

            <div id="weatherCard" class="weather-card hidden">
                <div class="weather-header">
                    <h2 id="cityName"></h2>
                    <div class="weather-icon" id="weatherIcon"></div>
                </div>

                <div class="weather-main">
                    <div class="temperature">
                        <span class="temp-value" id="temperature"></span>
                        <span class="temp-unit">°C</span>
                    </div>
                    <div class="weather-description" id="weatherDescription"></div>
                </div>

                <div class="weather-details">
                    <div class="detail-item">
                        <span class="detail-label">体感温度</span>
                        <span class="detail-value" id="feelsLike"></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">湿度</span>
                        <span class="detail-value" id="humidity"></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">气压</span>
                        <span class="detail-value" id="pressure"></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">风速</span>
                        <span class="detail-value" id="windSpeed"></span>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>天气数据更新时间: <span id="updateTime"></span></p>
            <p style="margin-top: 0.5rem;">
                <a href="setup.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none;">
                    ⚙️ 配置API密钥获取实时数据
                </a>
            </p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
