// 天气应用 JavaScript 代码

class WeatherApp {
    constructor() {
        this.initializeElements();
        this.bindEvents();
        this.loadDefaultWeather();
    }

    initializeElements() {
        this.searchForm = document.getElementById('searchForm');
        this.cityInput = document.getElementById('cityInput');
        this.searchButton = document.getElementById('searchButton');
        this.errorMessage = document.getElementById('errorMessage');
        this.loading = document.getElementById('loading');
        this.weatherCard = document.getElementById('weatherCard');
        this.cityName = document.getElementById('cityName');
        this.weatherIcon = document.getElementById('weatherIcon');
        this.temperature = document.getElementById('temperature');
        this.weatherDescription = document.getElementById('weatherDescription');
        this.feelsLike = document.getElementById('feelsLike');
        this.humidity = document.getElementById('humidity');
        this.pressure = document.getElementById('pressure');
        this.windSpeed = document.getElementById('windSpeed');
        this.updateTime = document.getElementById('updateTime');
    }

    bindEvents() {
        this.searchForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const city = this.cityInput.value.trim();
            if (city) {
                this.fetchWeather(city);
            }
        });
    }

    loadDefaultWeather() {
        this.fetchWeather('北京');
    }

    showLoading() {
        this.hideAll();
        this.loading.classList.remove('hidden');
        this.searchButton.disabled = true;
        this.searchButton.textContent = '搜索中...';
    }

    hideLoading() {
        this.loading.classList.add('hidden');
        this.searchButton.disabled = false;
        this.searchButton.textContent = '搜索';
    }

    showError(message) {
        this.hideAll();
        this.errorMessage.textContent = `⚠️ ${message}`;
        this.errorMessage.classList.remove('hidden');
    }

    showWeather() {
        this.hideAll();
        this.weatherCard.classList.remove('hidden');
    }

    hideAll() {
        this.errorMessage.classList.add('hidden');
        this.loading.classList.add('hidden');
        this.weatherCard.classList.add('hidden');
    }

    getWeatherIcon(iconCode, weatherMain) {
        const iconMap = {
            '01d': '☀️', '01n': '🌙',
            '02d': '⛅', '02n': '☁️',
            '03d': '☁️', '03n': '☁️',
            '04d': '☁️', '04n': '☁️',
            '09d': '🌧️', '09n': '🌧️',
            '10d': '🌦️', '10n': '🌧️',
            '11d': '⛈️', '11n': '⛈️',
            '13d': '❄️', '13n': '❄️',
            '50d': '🌫️', '50n': '🌫️'
        };

        // 如果有图标代码，使用图标映射
        if (iconCode && iconMap[iconCode]) {
            return iconMap[iconCode];
        }

        // 根据天气主要类型返回图标
        const weatherIconMap = {
            'Clear': '☀️',
            'Clouds': '☁️',
            'Rain': '🌧️',
            'Drizzle': '🌦️',
            'Thunderstorm': '⛈️',
            'Snow': '❄️',
            'Mist': '🌫️',
            'Fog': '🌫️',
            'Haze': '🌫️'
        };

        return weatherIconMap[weatherMain] || '🌤️';
    }

    generateMockWeatherData(cityName) {
        const weatherTypes = [
            { main: 'Clear', description: '晴天', icon: '01d' },
            { main: 'Clouds', description: '多云', icon: '02d' },
            { main: 'Rain', description: '小雨', icon: '10d' },
            { main: 'Snow', description: '雪', icon: '13d' }
        ];

        const randomWeather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];

        return {
            name: cityName,
            main: {
                temp: Math.floor(Math.random() * 30) + 5, // 5-35°C
                feels_like: Math.floor(Math.random() * 30) + 5,
                humidity: Math.floor(Math.random() * 50) + 30, // 30-80%
                pressure: Math.floor(Math.random() * 100) + 1000 // 1000-1100 hPa
            },
            weather: [randomWeather],
            wind: {
                speed: Math.floor(Math.random() * 10) + 1 // 1-10 m/s
            },
            visibility: Math.floor(Math.random() * 5000) + 5000 // 5-10 km
        };
    }

    async fetchWeather(cityName) {
        this.showLoading();

        try {
            // 注意：这里使用模拟数据，因为需要真实的 API 密钥
            // 在实际应用中，您需要：
            // 1. 注册 OpenWeatherMap 账户
            // 2. 获取 API 密钥
            // 3. 替换下面的 API_KEY

            const API_KEY = 'your_api_key_here'; // 替换为您的真实 API 密钥
            const API_URL = `https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(cityName)}&appid=${API_KEY}&units=metric&lang=zh_cn`;

            // 由于没有真实的 API 密钥，我们使用模拟数据
            if (API_KEY === 'your_api_key_here') {
                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const mockData = this.generateMockWeatherData(cityName);
                this.displayWeather(mockData);
                this.showError('使用模拟数据 (需要真实API密钥获取实时数据)');
                return;
            }

            // 真实 API 调用代码（需要有效的 API 密钥）
            const response = await fetch(API_URL);
            
            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('找不到该城市，请检查城市名称');
                } else if (response.status === 401) {
                    throw new Error('API 密钥无效');
                } else {
                    throw new Error('无法获取天气数据');
                }
            }

            const data = await response.json();
            this.displayWeather(data);

        } catch (error) {
            console.error('获取天气数据失败:', error);
            this.showError(error.message || '获取天气数据失败，请稍后重试');
        } finally {
            this.hideLoading();
        }
    }

    displayWeather(data) {
        // 更新城市名称
        this.cityName.textContent = data.name;

        // 更新天气图标
        const weather = data.weather[0];
        this.weatherIcon.textContent = this.getWeatherIcon(weather.icon, weather.main);

        // 更新温度
        this.temperature.textContent = Math.round(data.main.temp);

        // 更新天气描述
        this.weatherDescription.textContent = weather.description;

        // 更新详细信息
        this.feelsLike.textContent = `${Math.round(data.main.feels_like)}°C`;
        this.humidity.textContent = `${data.main.humidity}%`;
        this.pressure.textContent = `${data.main.pressure} hPa`;
        this.windSpeed.textContent = `${data.wind.speed} m/s`;

        // 更新时间
        this.updateTime.textContent = new Date().toLocaleString('zh-CN');

        // 显示天气卡片
        this.showWeather();
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new WeatherApp();
});

// 添加一些实用功能
document.addEventListener('keydown', (e) => {
    // 按 Escape 键清除错误信息
    if (e.key === 'Escape') {
        const errorMessage = document.getElementById('errorMessage');
        if (!errorMessage.classList.contains('hidden')) {
            errorMessage.classList.add('hidden');
        }
    }
});

// 添加城市输入建议功能
const popularCities = [
    '北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', 
    '重庆', '天津', '西安', '青岛', '大连', '厦门', '苏州', '长沙'
];

// 简单的城市名称验证
function validateCityName(cityName) {
    if (!cityName || cityName.trim().length === 0) {
        return false;
    }
    
    // 检查是否包含特殊字符（除了中文、英文、空格、连字符）
    const validPattern = /^[\u4e00-\u9fa5a-zA-Z\s\-]+$/;
    return validPattern.test(cityName.trim());
}

// 为输入框添加一些交互效果
const cityInput = document.getElementById('cityInput');
if (cityInput) {
    cityInput.addEventListener('input', (e) => {
        const value = e.target.value;
        if (!validateCityName(value) && value.length > 0) {
            e.target.style.borderColor = '#ff6b6b';
        } else {
            e.target.style.borderColor = '';
        }
    });
}
