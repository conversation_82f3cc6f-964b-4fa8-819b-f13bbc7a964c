// 天气应用 JavaScript 代码

class WeatherApp {
    constructor() {
        this.initializeElements();
        this.bindEvents();
        this.loadDefaultWeather();
    }

    initializeElements() {
        this.searchForm = document.getElementById('searchForm');
        this.cityInput = document.getElementById('cityInput');
        this.searchButton = document.getElementById('searchButton');
        this.errorMessage = document.getElementById('errorMessage');
        this.loading = document.getElementById('loading');
        this.weatherCard = document.getElementById('weatherCard');
        this.cityName = document.getElementById('cityName');
        this.weatherIcon = document.getElementById('weatherIcon');
        this.temperature = document.getElementById('temperature');
        this.weatherDescription = document.getElementById('weatherDescription');
        this.feelsLike = document.getElementById('feelsLike');
        this.humidity = document.getElementById('humidity');
        this.pressure = document.getElementById('pressure');
        this.windSpeed = document.getElementById('windSpeed');
        this.updateTime = document.getElementById('updateTime');
    }

    bindEvents() {
        this.searchForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const city = this.cityInput.value.trim();
            if (city) {
                this.fetchWeather(city);
            }
        });
    }

    loadDefaultWeather() {
        this.fetchWeather('北京');
    }

    showLoading() {
        this.hideAll();
        this.loading.classList.remove('hidden');
        this.searchButton.disabled = true;
        this.searchButton.textContent = '搜索中...';
    }

    hideLoading() {
        this.loading.classList.add('hidden');
        this.searchButton.disabled = false;
        this.searchButton.textContent = '搜索';
    }

    showError(message) {
        this.hideAll();
        this.errorMessage.textContent = `⚠️ ${message}`;
        this.errorMessage.classList.remove('hidden');
    }

    showWeather() {
        this.hideAll();
        this.weatherCard.classList.remove('hidden');
    }

    hideAll() {
        this.errorMessage.classList.add('hidden');
        this.loading.classList.add('hidden');
        this.weatherCard.classList.add('hidden');
    }

    getWeatherIcon(iconCode, weatherMain) {
        // 和风天气图标映射
        const iconMap = {
            // 晴天
            '100': '☀️', // 晴
            // 多云
            '101': '⛅', // 多云
            '102': '🌤️', // 少云
            '103': '⛅', // 晴间多云
            '104': '☁️', // 阴
            // 雨天
            '300': '🌦️', // 阵雨
            '301': '🌧️', // 强阵雨
            '302': '⛈️', // 雷阵雨
            '303': '⛈️', // 强雷阵雨
            '304': '⛈️', // 雷阵雨伴有冰雹
            '305': '🌧️', // 小雨
            '306': '🌧️', // 中雨
            '307': '🌧️', // 大雨
            '308': '🌧️', // 极端降雨
            '309': '🌦️', // 毛毛雨/细雨
            '310': '🌧️', // 暴雨
            '311': '🌧️', // 大暴雨
            '312': '🌧️', // 特大暴雨
            '313': '🌧️', // 冻雨
            // 雪天
            '400': '❄️', // 小雪
            '401': '❄️', // 中雪
            '402': '❄️', // 大雪
            '403': '❄️', // 暴雪
            '404': '🌨️', // 雨夹雪
            '405': '🌨️', // 雨雪天气
            '406': '🌨️', // 阵雨夹雪
            '407': '❄️', // 阵雪
            // 雾霾等
            '500': '🌫️', // 薄雾
            '501': '🌫️', // 雾
            '502': '😷', // 霾
            '503': '💨', // 扬沙
            '504': '💨', // 浮尘
            '507': '💨', // 沙尘暴
            '508': '💨', // 强沙尘暴
            '509': '🌫️', // 浓雾
            '510': '🌫️', // 强浓雾
            '511': '😷', // 中度霾
            '512': '😷', // 重度霾
            '513': '😷'  // 严重霾
        };

        // 如果有图标代码，使用图标映射
        if (iconCode && iconMap[iconCode]) {
            return iconMap[iconCode];
        }

        // 根据天气主要类型返回图标（后备方案）
        const weatherIconMap = {
            'Clear': '☀️',
            'Clouds': '☁️',
            'Rain': '🌧️',
            'Drizzle': '🌦️',
            'Thunderstorm': '⛈️',
            'Snow': '❄️',
            'Mist': '🌫️',
            'Fog': '🌫️',
            'Haze': '🌫️'
        };

        return weatherIconMap[weatherMain] || '🌤️';
    }

    generateMockWeatherData(cityName) {
        const weatherTypes = [
            { main: 'Clear', description: '晴天', icon: '01d' },
            { main: 'Clouds', description: '多云', icon: '02d' },
            { main: 'Rain', description: '小雨', icon: '10d' },
            { main: 'Snow', description: '雪', icon: '13d' }
        ];

        const randomWeather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];

        return {
            name: cityName,
            main: {
                temp: Math.floor(Math.random() * 30) + 5, // 5-35°C
                feels_like: Math.floor(Math.random() * 30) + 5,
                humidity: Math.floor(Math.random() * 50) + 30, // 30-80%
                pressure: Math.floor(Math.random() * 100) + 1000 // 1000-1100 hPa
            },
            weather: [randomWeather],
            wind: {
                speed: Math.floor(Math.random() * 10) + 1 // 1-10 m/s
            },
            visibility: Math.floor(Math.random() * 5000) + 5000 // 5-10 km
        };
    }

    async fetchWeather(cityName) {
        this.showLoading();

        try {
            // 使用和风天气API
            // 注意：在实际使用中，您需要注册并获取API密钥
            const API_KEY = '2c8285b29a534a8984343c4bd5d4cc61'; // 替换为您的和风天气API密钥

            // 和风天气API配置
            // 注意：新版本需要使用个人API Host，格式如：abc1234xyz.def.qweatherapi.com
            // 如果您有个人API Host，请替换下面的地址
            const API_HOST = 'devapi.qweather.com'; // 替换为您的API Host

            // 尝试多个可能的API地址
            const apiEndpoints = [
                // 新版本个人API Host格式
                `https://${API_HOST}/geo/v2/city/lookup`,
                // 开发版API地址
                'https://devapi.qweather.com/geo/v2/city/lookup',
                // 旧版本地址（作为后备）
                'https://geoapi.qweather.com/v2/city/lookup'
            ];

            let geoData = null;
            let lastError = null;

            // 尝试不同的API地址
            for (const endpoint of apiEndpoints) {
                try {
                    const geoUrl = `${endpoint}?location=${encodeURIComponent(cityName)}&key=${API_KEY}`;
                    console.log('尝试请求GeoAPI:', geoUrl);

                    const geoResponse = await fetch(geoUrl);
                    console.log('GeoAPI响应状态:', geoResponse.status);

                    if (geoResponse.ok) {
                        geoData = await geoResponse.json();
                        console.log('GeoAPI响应数据:', geoData);

                        if (geoData.code === '200' && geoData.location && geoData.location.length > 0) {
                            console.log('成功获取城市数据，使用API地址:', endpoint);
                            break; // 成功获取数据，跳出循环
                        } else {
                            console.warn('API返回错误:', geoData);
                            lastError = new Error(`API返回错误: ${geoData.code} - ${geoData.message || '未知错误'}`);
                        }
                    } else {
                        console.warn('API请求失败:', geoResponse.status, geoResponse.statusText);
                        lastError = new Error(`API请求失败: ${geoResponse.status} ${geoResponse.statusText}`);
                    }
                } catch (error) {
                    console.warn('请求失败:', endpoint, error.message);
                    lastError = error;
                }
            }

            // 如果所有API地址都失败了
            if (!geoData || geoData.code !== '200' || !geoData.location || geoData.location.length === 0) {
                console.error('所有API地址都失败了，使用模拟数据');
                const mockData = this.generateMockWeatherData(cityName);
                this.displayWeather(mockData);
                this.showError(`无法连接到和风天气API，使用模拟数据。错误: ${lastError?.message || '网络连接失败'}`);
                return;
            }
            const locationId = geoData.location[0].id;

            // 获取实时天气数据
            const weatherEndpoints = [
                `https://${API_HOST}/v7/weather/now`,
                'https://devapi.qweather.com/v7/weather/now'
            ];

            let weatherData = null;
            for (const endpoint of weatherEndpoints) {
                try {
                    const weatherUrl = `${endpoint}?location=${locationId}&key=${API_KEY}`;
                    console.log('尝试请求天气API:', weatherUrl);

                    const weatherResponse = await fetch(weatherUrl);

                    if (weatherResponse.ok) {
                        weatherData = await weatherResponse.json();
                        console.log('天气API响应数据:', weatherData);

                        if (weatherData.code === '200') {
                            console.log('成功获取天气数据，使用API地址:', endpoint);
                            break;
                        }
                    }
                } catch (error) {
                    console.warn('天气API请求失败:', endpoint, error.message);
                }
            }

            if (!weatherData || weatherData.code !== '200') {
                throw new Error('无法获取天气数据');
            }

            // 转换和风天气数据格式为应用所需格式
            const convertedData = this.convertQWeatherData(weatherData, geoData.location[0]);
            this.displayWeather(convertedData);

        } catch (error) {
            console.error('获取天气数据失败:', error);
            // 提供模拟数据作为后备
            const mockData = this.generateMockWeatherData(cityName);
            this.displayWeather(mockData);
            this.showError(`${error.message} (使用模拟数据)`);
        } finally {
            this.hideLoading();
        }
    }

    // 转换和风天气数据格式
    convertQWeatherData(weatherData, locationData) {
        const now = weatherData.now;

        return {
            name: locationData.name,
            main: {
                temp: parseInt(now.temp),
                feels_like: parseInt(now.feelsLike),
                humidity: parseInt(now.humidity),
                pressure: parseInt(now.pressure)
            },
            weather: [{
                main: this.getWeatherMainFromIcon(now.icon),
                description: now.text,
                icon: now.icon
            }],
            wind: {
                speed: parseFloat(now.windSpeed),
                dir: now.windDir
            },
            visibility: parseFloat(now.vis) * 1000 // 转换为米
        };
    }

    // 根据和风天气图标代码获取主要天气类型
    getWeatherMainFromIcon(iconCode) {
        const iconMap = {
            '100': 'Clear', // 晴
            '101': 'Clouds', // 多云
            '102': 'Clouds', // 少云
            '103': 'Clouds', // 晴间多云
            '104': 'Clouds', // 阴
            '300': 'Rain', // 阵雨
            '301': 'Rain', // 强阵雨
            '302': 'Thunderstorm', // 雷阵雨
            '303': 'Thunderstorm', // 强雷阵雨
            '304': 'Rain', // 雷阵雨伴有冰雹
            '305': 'Rain', // 小雨
            '306': 'Rain', // 中雨
            '307': 'Rain', // 大雨
            '308': 'Rain', // 极端降雨
            '309': 'Drizzle', // 毛毛雨/细雨
            '310': 'Rain', // 暴雨
            '311': 'Rain', // 大暴雨
            '312': 'Rain', // 特大暴雨
            '313': 'Rain', // 冻雨
            '400': 'Snow', // 小雪
            '401': 'Snow', // 中雪
            '402': 'Snow', // 大雪
            '403': 'Snow', // 暴雪
            '404': 'Snow', // 雨夹雪
            '405': 'Snow', // 雨雪天气
            '406': 'Snow', // 阵雨夹雪
            '407': 'Snow', // 阵雪
            '500': 'Mist', // 薄雾
            '501': 'Fog', // 雾
            '502': 'Haze', // 霾
            '503': 'Dust', // 扬沙
            '504': 'Dust', // 浮尘
            '507': 'Dust', // 沙尘暴
            '508': 'Dust', // 强沙尘暴
            '509': 'Fog', // 浓雾
            '510': 'Fog', // 强浓雾
            '511': 'Haze', // 中度霾
            '512': 'Haze', // 重度霾
            '513': 'Haze' // 严重霾
        };

        return iconMap[iconCode] || 'Clear';
    }

    displayWeather(data) {
        // 更新城市名称
        this.cityName.textContent = data.name;

        // 更新天气图标
        const weather = data.weather[0];
        this.weatherIcon.textContent = this.getWeatherIcon(weather.icon, weather.main);

        // 更新温度
        this.temperature.textContent = Math.round(data.main.temp);

        // 更新天气描述
        this.weatherDescription.textContent = weather.description;

        // 更新详细信息
        this.feelsLike.textContent = `${Math.round(data.main.feels_like)}°C`;
        this.humidity.textContent = `${data.main.humidity}%`;
        this.pressure.textContent = `${data.main.pressure} hPa`;
        this.windSpeed.textContent = `${data.wind.speed} m/s`;

        // 更新时间
        this.updateTime.textContent = new Date().toLocaleString('zh-CN');

        // 显示天气卡片
        this.showWeather();
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new WeatherApp();
});

// 添加一些实用功能
document.addEventListener('keydown', (e) => {
    // 按 Escape 键清除错误信息
    if (e.key === 'Escape') {
        const errorMessage = document.getElementById('errorMessage');
        if (!errorMessage.classList.contains('hidden')) {
            errorMessage.classList.add('hidden');
        }
    }
});

// 添加城市输入建议功能
const popularCities = [
    '北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', 
    '重庆', '天津', '西安', '青岛', '大连', '厦门', '苏州', '长沙'
];

// 简单的城市名称验证
function validateCityName(cityName) {
    if (!cityName || cityName.trim().length === 0) {
        return false;
    }
    
    // 检查是否包含特殊字符（除了中文、英文、空格、连字符）
    const validPattern = /^[\u4e00-\u9fa5a-zA-Z\s\-]+$/;
    return validPattern.test(cityName.trim());
}

// 为输入框添加一些交互效果
const cityInput = document.getElementById('cityInput');
if (cityInput) {
    cityInput.addEventListener('input', (e) => {
        const value = e.target.value;
        if (!validateCityName(value) && value.length > 0) {
            e.target.style.borderColor = '#ff6b6b';
        } else {
            e.target.style.borderColor = '';
        }
    });
}
